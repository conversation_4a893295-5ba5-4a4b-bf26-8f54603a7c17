import os
import numpy as np
import hydra
import napari
import random
import gc
import psutil
import time
from qtpy.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QComboBox,
    QCheckBox,
    QLabel,
    QPushButton,
    QFileDialog,
    QMessageBox,
)

from dataprocess.volume import Volume


def generate_distinct_colors(n):
    """
    Generate n visually distinct colors for instance segmentation visualization

    Args:
        n: Number of colors to generate

    Returns:
        Dictionary mapping instance IDs to RGB colors
    """
    colors = {}
    # Start with some predefined distinct colors for the first few instances
    base_colors = [
        [1, 0, 0],  # Red
        [0, 1, 0],  # Green
        [0, 0, 1],  # Blue
        [1, 1, 0],  # Yellow
        [1, 0, 1],  # Magenta
        [0, 1, 1],  # <PERSON>an
        [1, 0.5, 0],  # Orange
        [0.5, 0, 1],  # Purple
        [0, 0.5, 1],  # Sky blue
        [1, 0, 0.5],  # Pink
    ]

    # Use predefined colors first
    for i in range(min(n, len(base_colors))):
        colors[i + 1] = base_colors[i]  # +1 because instance IDs typically start at 1

    # Generate additional random colors if needed
    for i in range(len(base_colors) + 1, n + 1):
        # Generate colors in HSV space for better visual distinction
        h = random.random()  # Hue
        s = 0.7 + random.random() * 0.3  # Saturation (0.7-1.0)
        v = 0.7 + random.random() * 0.3  # Value (0.7-1.0)

        # Convert HSV to RGB
        h_i = int(h * 6)
        f = h * 6 - h_i
        p = v * (1 - s)
        q = v * (1 - f * s)
        t = v * (1 - (1 - f) * s)

        if h_i == 0:
            r, g, b = v, t, p
        elif h_i == 1:
            r, g, b = q, v, p
        elif h_i == 2:
            r, g, b = p, v, t
        elif h_i == 3:
            r, g, b = p, q, v
        elif h_i == 4:
            r, g, b = t, p, v
        else:
            r, g, b = v, p, q

        colors[i] = [r, g, b]

    return colors


class ViewerControls(QWidget):
    def __init__(self, viewer, volume=None, pred=None, true=None):
        super().__init__()
        self.viewer = viewer
        self.volume = volume
        self.pred = pred
        self.true = true

        # Store original layers
        self.volume_layer = None
        self.pred_layer = None
        self.true_layer = None

        # Current view direction
        self.current_direction = "z"

        # Visualization mode
        self.instance_mode_pred = False
        self.instance_mode_true = False

        # Store original instance data
        self.pred_instances = None
        self.true_instances = None

        # Cache for instance colors
        self.pred_colors = (
            None  # Dictionary of colors generated by generate_distinct_colors
        )
        self.true_colors = (
            None  # Dictionary of colors generated by generate_distinct_colors
        )

        # Cache for instance color maps (mapping from instance ID to RGB color)
        self.pred_color_map = None  # Dictionary mapping instance IDs to RGB colors
        self.true_color_map = None  # Dictionary mapping instance IDs to RGB colors

        # File paths for loaded data
        self.volume_path = None
        self.pred_path = None
        self.true_path = None

        # Try to register cleanup handler when viewer is closed
        # Different napari versions have different event names
        if hasattr(self.viewer.events, "closed"):
            self.viewer.events.closed.connect(self.cleanup)
        elif hasattr(self.viewer.events, "close"):
            self.viewer.events.close.connect(self.cleanup)
        else:
            print("Warning: Could not register cleanup handler for viewer close event")

        # Set up the user interface
        self.setup_ui()

    def __del__(self):
        """Destructor to ensure resources are freed"""
        self.cleanup()

    def cleanup(self, event=None):
        """Clean up resources to prevent memory leaks"""
        print("Cleaning up resources...")
        # Remove layers
        self.remove_layers()

        # Clear references to large data
        self.volume = None
        self.pred = None
        self.true = None
        self.pred_instances = None
        self.true_instances = None
        self.pred_colors = None
        self.true_colors = None
        self.pred_color_map = None
        self.true_color_map = None

        # Force garbage collection
        gc.collect()

    # Create layout and UI elements
    def setup_ui(self):
        """Set up the user interface"""
        layout = QVBoxLayout()
        self.setLayout(layout)

        # File loading section
        file_layout = QVBoxLayout()
        file_layout.addWidget(QLabel("Load Files:"))

        # Volume file selection
        volume_layout = QHBoxLayout()
        self.volume_button = QPushButton("Load Volume")
        self.volume_button.clicked.connect(self.load_volume_file)
        volume_layout.addWidget(self.volume_button)
        self.volume_label = QLabel("No volume loaded")
        volume_layout.addWidget(self.volume_label)
        file_layout.addLayout(volume_layout)

        # Prediction mask file selection
        pred_layout = QHBoxLayout()
        self.pred_button = QPushButton("Load Prediction Mask")
        self.pred_button.clicked.connect(self.load_pred_file)
        pred_layout.addWidget(self.pred_button)
        self.pred_label = QLabel("No prediction mask loaded")
        pred_layout.addWidget(self.pred_label)
        file_layout.addLayout(pred_layout)

        # Ground truth mask file selection
        true_layout = QHBoxLayout()
        self.true_button = QPushButton("Load Ground Truth Mask")
        self.true_button.clicked.connect(self.load_true_file)
        true_layout.addWidget(self.true_button)
        self.true_label = QLabel("No ground truth mask loaded")
        true_layout.addWidget(self.true_label)
        file_layout.addLayout(true_layout)

        layout.addLayout(file_layout)

        # Direction selection
        direction_layout = QHBoxLayout()
        direction_layout.addWidget(QLabel("View Direction:"))
        self.direction_combo = QComboBox()
        self.direction_combo.addItems(["z", "y", "x"])
        self.direction_combo.currentTextChanged.connect(self.change_direction)
        direction_layout.addWidget(self.direction_combo)
        layout.addLayout(direction_layout)

        # Visibility controls
        visibility_layout = QHBoxLayout()

        # Pred mask checkbox
        self.pred_checkbox = QCheckBox("Show Prediction")
        self.pred_checkbox.setChecked(True)
        self.pred_checkbox.stateChanged.connect(self.toggle_pred)
        visibility_layout.addWidget(self.pred_checkbox)

        # True mask checkbox
        self.true_checkbox = QCheckBox("Show Ground Truth")
        self.true_checkbox.setChecked(True)
        self.true_checkbox.stateChanged.connect(self.toggle_true)
        visibility_layout.addWidget(self.true_checkbox)

        layout.addLayout(visibility_layout)

        # Instance segmentation mode controls
        instance_layout = QHBoxLayout()

        # Prediction instance mode checkbox
        self.pred_instance_checkbox = QCheckBox("Prediction Instance Mode")
        self.pred_instance_checkbox.setChecked(False)
        self.pred_instance_checkbox.stateChanged.connect(self.toggle_pred_instance_mode)
        instance_layout.addWidget(self.pred_instance_checkbox)

        # Ground truth instance mode checkbox
        self.true_instance_checkbox = QCheckBox("Ground Truth Instance Mode")
        self.true_instance_checkbox.setChecked(False)
        self.true_instance_checkbox.stateChanged.connect(self.toggle_true_instance_mode)
        instance_layout.addWidget(self.true_instance_checkbox)

        layout.addLayout(instance_layout)

        # Store the original instance data if available
        self.store_instance_data()

        # Initialize the view
        self.initialize_view()

    def load_volume_file(self):
        """Load volume file using file dialog"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Volume File",
            "",
            "Volume files (*.zst *.tif *.tiff *.nii *.nii.gz);;All files (*.*)",
        )
        if file_path:
            try:
                # Load the volume
                volume = Volume(file_path)
                volume.load()
                volume.scale_volume_to((512, 512, 512))

                self.volume = volume.volume
                self.volume_path = file_path
                self.volume_label.setText(f"Volume: {os.path.basename(file_path)}")

                # Refresh the view
                self.refresh_view()
                print(f"Successfully loaded volume: {file_path}")

            except Exception as e:
                QMessageBox.critical(
                    self, "Error", f"Failed to load volume file:\n{str(e)}"
                )
                print(f"Error loading volume file: {e}")

    def load_pred_file(self):
        """Load prediction mask file using file dialog"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Prediction Mask File",
            "",
            "Mask files (*.zst *.tif *.tiff *.nii *.nii.gz);;All files (*.*)",
        )
        if file_path:
            try:
                # Load the prediction mask
                pred_mask = Volume(file_path)
                pred_mask.load()
                pred_mask.scale_volume_to((512, 512, 512))

                self.pred = pred_mask.volume
                self.pred_path = file_path
                self.pred_label.setText(f"Prediction: {os.path.basename(file_path)}")

                # Enable prediction controls
                self.pred_checkbox.setEnabled(True)
                self.pred_instance_checkbox.setEnabled(True)

                # Store instance data for the new prediction
                self.store_pred_instance_data()

                # Refresh the view
                self.refresh_view()
                print(f"Successfully loaded prediction mask: {file_path}")

            except Exception as e:
                QMessageBox.critical(
                    self, "Error", f"Failed to load prediction mask file:\n{str(e)}"
                )
                print(f"Error loading prediction mask file: {e}")

    def load_true_file(self):
        """Load ground truth mask file using file dialog"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Ground Truth Mask File",
            "",
            "Mask files (*.zst *.tif *.tiff *.nii *.nii.gz);;All files (*.*)",
        )
        if file_path:
            try:
                # Load the ground truth mask
                true_mask = Volume(file_path)
                true_mask.load()
                true_mask.scale_volume_to((512, 512, 512))

                self.true = true_mask.volume
                self.true_path = file_path
                self.true_label.setText(f"Ground Truth: {os.path.basename(file_path)}")

                # Enable ground truth controls
                self.true_checkbox.setEnabled(True)
                self.true_instance_checkbox.setEnabled(True)

                # Store instance data for the new ground truth
                self.store_true_instance_data()

                # Refresh the view
                self.refresh_view()
                print(f"Successfully loaded ground truth mask: {file_path}")

            except Exception as e:
                QMessageBox.critical(
                    self, "Error", f"Failed to load ground truth mask file:\n{str(e)}"
                )
                print(f"Error loading ground truth mask file: {e}")

    def refresh_view(self):
        """Refresh the entire view after loading new data"""
        # Remove existing layers
        self.remove_layers()

        # Re-initialize the view with new data
        self.initialize_view()

    def store_instance_data(self):
        """Store the original instance data before any conversion and pre-compute color maps"""
        self.store_pred_instance_data()
        self.store_true_instance_data()

    def store_pred_instance_data(self):
        """Store prediction instance data and pre-compute color maps"""
        # Clear existing prediction instance data
        self.pred_instances = None
        self.pred_colors = None
        self.pred_color_map = None

        if self.pred is not None:
            try:
                # Get unique values without loading the entire array into memory
                # Sample a subset of the data to find unique values
                sample_size = min(
                    1000000, self.pred.size
                )  # Limit sample size to reduce memory usage
                indices = np.random.choice(self.pred.size, sample_size, replace=False)
                sample = self.pred.flat[indices]
                unique_pred_ids = np.unique(sample)
                unique_pred_ids = unique_pred_ids[
                    unique_pred_ids > 0
                ]  # Exclude background

                # Print debug info
                print(
                    f"Prediction unique values (sampled): {unique_pred_ids[:10]}{'...' if len(unique_pred_ids) > 10 else ''}"
                )
                print(
                    f"Prediction max value (sampled): {np.max(sample) if sample.size > 0 else 'None'}"
                )

                # Check if we have instance IDs
                if len(unique_pred_ids) > 0:
                    # Don't copy the data, just reference it
                    self.pred_instances = self.pred
                    # Generate colors for prediction instances
                    self.pred_colors = generate_distinct_colors(len(unique_pred_ids))

                    # Pre-compute color map for prediction instances
                    self.pred_color_map = {}
                    for i, instance_id in enumerate(unique_pred_ids):
                        # Use modulo to cycle through available colors if we have more instances than colors
                        color_idx = (i % len(self.pred_colors)) + 1
                        self.pred_color_map[instance_id] = self.pred_colors[color_idx]

                    print(
                        f"Stored {len(unique_pred_ids)} prediction instances with pre-computed color map"
                    )

                    # Force garbage collection after processing
                    gc.collect()
                else:
                    print("No prediction instances found")
            except Exception as e:
                print(f"Error processing prediction mask: {e}")
                self.pred_instances = None
                self.pred_colors = None
                self.pred_color_map = None
        else:
            print("No prediction mask provided")
            if hasattr(self, "pred_instance_checkbox"):
                self.pred_instance_checkbox.setEnabled(False)

    def store_true_instance_data(self):
        """Store ground truth instance data and pre-compute color maps"""
        # Clear existing ground truth instance data
        self.true_instances = None
        self.true_colors = None
        self.true_color_map = None

        if self.true is not None:
            try:
                # Get unique values without loading the entire array into memory
                # Sample a subset of the data to find unique values
                sample_size = min(
                    1000000, self.true.size
                )  # Limit sample size to reduce memory usage
                indices = np.random.choice(self.true.size, sample_size, replace=False)
                sample = self.true.flat[indices]
                unique_true_ids = np.unique(sample)
                unique_true_ids = unique_true_ids[
                    unique_true_ids > 0
                ]  # Exclude background

                # Print debug info
                print(
                    f"Ground truth unique values (sampled): {unique_true_ids[:10]}{'...' if len(unique_true_ids) > 10 else ''}"
                )
                print(
                    f"Ground truth max value (sampled): {np.max(sample) if sample.size > 0 else 'None'}"
                )

                # Check if we have instance IDs
                if len(unique_true_ids) > 0:
                    # Don't copy the data, just reference it
                    self.true_instances = self.true
                    # Generate colors for ground truth instances
                    self.true_colors = generate_distinct_colors(len(unique_true_ids))

                    # Pre-compute color map for ground truth instances
                    self.true_color_map = {}
                    for i, instance_id in enumerate(unique_true_ids):
                        # Use modulo to cycle through available colors if we have more instances than colors
                        color_idx = (i % len(self.true_colors)) + 1
                        self.true_color_map[instance_id] = self.true_colors[color_idx]

                    print(
                        f"Stored {len(unique_true_ids)} ground truth instances with pre-computed color map"
                    )

                    # Force garbage collection after processing
                    gc.collect()
                else:
                    print("No ground truth instances found")
            except Exception as e:
                print(f"Error processing ground truth mask: {e}")
                self.true_instances = None
                self.true_colors = None
                self.true_color_map = None
        else:
            print("No ground truth mask provided")
            if hasattr(self, "true_instance_checkbox"):
                self.true_instance_checkbox.setEnabled(False)

    def create_instance_color_map(self, data, colors):
        """Create a color map for instance segmentation

        Args:
            data: The instance segmentation data
            colors: Dictionary of colors generated by generate_distinct_colors

        Returns:
            Dictionary mapping instance IDs to RGB colors
        """
        instance_colors = {}
        # Get unique instance IDs from the actual data
        unique_ids = np.unique(data)
        unique_ids = unique_ids[unique_ids > 0]  # Exclude background

        # Assign colors to each instance ID
        for i, instance_id in enumerate(unique_ids):
            # Use modulo to cycle through available colors if we have more instances than colors
            color_idx = (i % len(colors)) + 1
            instance_colors[instance_id] = colors[color_idx]

        return instance_colors

    def add_mask_layer(
        self,
        data,
        name,
        is_instance_mode,
        colors,
        visible,
        default_color="red",
        color_map=None,
    ):
        """Add a mask layer to the viewer

        Args:
            data: The mask data (can be None)
            name: Name of the layer
            is_instance_mode: Whether to use instance segmentation mode
            colors: Dictionary of colors for instance segmentation (used only if color_map is None)
            visible: Whether the layer should be visible
            default_color: Default color for binary mask
            color_map: Pre-computed color map mapping instance IDs to RGB colors

        Returns:
            The created napari layer or None if data is None
        """
        # If data is None, don't create a layer
        if data is None:
            print(f"No data provided for {name} layer, skipping")
            return None

        if is_instance_mode:
            # Use instance segmentation mode
            print(f"Using instance mode for {name}")
            layer = self.viewer.add_labels(
                data,
                name=f"{name} (Instance)",
                blending="additive",
                visible=visible,
            )

            # Set colors for each instance
            if color_map:
                # Use pre-computed color map (more efficient)
                print(
                    f"Using pre-computed color map for {name} with {len(color_map)} instances"
                )
                layer.color = color_map
            elif colors:
                # Compute color map on-the-fly (less efficient)
                instance_colors = self.create_instance_color_map(data, colors)
                print(
                    f"Computing color map for {len(instance_colors)} {name} instances on-the-fly"
                )
                layer.color = instance_colors
        else:
            # Use binary mask mode
            binary_data = data > 0
            layer = self.viewer.add_labels(
                binary_data,
                name=f"{name} (Binary)",
                blending="additive",
                visible=visible,
            )
            # Set color for binary mask
            layer.color = {1: default_color}

        layer.opacity = 0.5
        return layer

    def initialize_view(self):
        """Initialize the napari viewer with the volume and masks"""
        # Add the volume layer if available
        if self.volume is not None:
            self.volume_layer = self.viewer.add_image(
                self.volume, name="Volume", colormap="gray", blending="additive"
            )
        else:
            self.volume_layer = None

        # Add the prediction mask layer if available
        if self.pred is not None:
            self.pred_layer = self.add_mask_layer(
                data=(
                    self.pred_instances
                    if self.instance_mode_pred and self.pred_instances is not None
                    else self.pred
                ),
                name="Prediction",
                is_instance_mode=self.instance_mode_pred
                and self.pred_instances is not None,
                colors=self.pred_colors,
                visible=self.pred_checkbox.isChecked(),
                default_color="red",
                color_map=self.pred_color_map if self.instance_mode_pred else None,
            )
            # Enable prediction controls
            self.pred_checkbox.setEnabled(True)
            if self.pred_instances is not None:
                self.pred_instance_checkbox.setEnabled(True)
        else:
            # No prediction mask available
            self.pred_layer = None
            # Disable the prediction checkbox
            self.pred_checkbox.setEnabled(False)
            self.pred_instance_checkbox.setEnabled(False)

        # Add the ground truth mask layer if available
        if self.true is not None:
            self.true_layer = self.add_mask_layer(
                data=(
                    self.true_instances
                    if self.instance_mode_true and self.true_instances is not None
                    else self.true
                ),
                name="Ground Truth",
                is_instance_mode=self.instance_mode_true
                and self.true_instances is not None,
                colors=self.true_colors,
                visible=self.true_checkbox.isChecked(),
                default_color="blue",
                color_map=self.true_color_map if self.instance_mode_true else None,
            )
            # Enable ground truth controls
            self.true_checkbox.setEnabled(True)
            if self.true_instances is not None:
                self.true_instance_checkbox.setEnabled(True)
        else:
            # No ground truth mask available
            self.true_layer = None
            # Disable the ground truth checkbox
            self.true_checkbox.setEnabled(False)
            self.true_instance_checkbox.setEnabled(False)

    def transpose_data(self, data, direction):
        """Transpose data based on viewing direction

        Args:
            data: The data to transpose
            direction: The viewing direction ('x', 'y', or 'z')

        Returns:
            Transposed data
        """
        if data is None:
            return None

        if direction == "z":
            return data
        elif direction == "y":
            return np.transpose(data, (1, 0, 2))
        elif direction == "x":
            return np.transpose(data, (2, 0, 1))
        else:
            raise ValueError(f"Invalid direction: {direction}")

    def clear_memory(self):
        """Clear memory and force garbage collection"""
        # Get current memory usage
        process = psutil.Process(os.getpid())
        before_mem = process.memory_info().rss / 1024 / 1024  # in MB

        # Force garbage collection
        gc.collect()

        # Get memory usage after garbage collection
        after_mem = process.memory_info().rss / 1024 / 1024  # in MB
        print(
            f"Memory usage: {before_mem:.1f} MB -> {after_mem:.1f} MB (freed {before_mem - after_mem:.1f} MB)"
        )

    def remove_layers(self):
        """Safely remove existing layers and clear references"""
        # Store current layer visibility states
        volume_visible = self.volume_layer.visible if self.volume_layer else True
        pred_visible = self.pred_layer.visible if self.pred_layer else True
        true_visible = self.true_layer.visible if self.true_layer else True

        # Remove existing layers
        if self.volume_layer and self.volume_layer in self.viewer.layers:
            self.viewer.layers.remove(self.volume_layer)
        if self.pred_layer and self.pred_layer in self.viewer.layers:
            self.viewer.layers.remove(self.pred_layer)
        if self.true_layer and self.true_layer in self.viewer.layers:
            self.viewer.layers.remove(self.true_layer)

        # Clear references to removed layers
        self.volume_layer = None
        self.pred_layer = None
        self.true_layer = None

        # Clear memory
        self.clear_memory()

        return volume_visible, pred_visible, true_visible

    def change_direction(self, direction):
        """Change the viewing direction"""
        start_time = time.time()
        self.current_direction = direction

        # Remove existing layers and get visibility states
        volume_visible, pred_visible, true_visible = self.remove_layers()

        # Add new volume layer with transposed data if volume exists
        if self.volume is not None:
            volume_data = self.transpose_data(self.volume, direction)
            self.volume_layer = self.viewer.add_image(
                volume_data,
                name="Volume",
                colormap="gray",
                blending="additive",
                visible=volume_visible,
            )
        else:
            self.volume_layer = None

        # Handle prediction mask if available
        if self.pred is not None:
            # Transpose prediction data
            if self.instance_mode_pred and self.pred_instances is not None:
                pred_data = self.transpose_data(self.pred_instances, direction)
            else:
                pred_data = self.transpose_data(self.pred, direction)

            # Add the prediction mask layer
            self.pred_layer = self.add_mask_layer(
                data=pred_data,
                name="Prediction",
                is_instance_mode=self.instance_mode_pred
                and self.pred_instances is not None,
                colors=self.pred_colors,
                visible=pred_visible,
                default_color="red",
                color_map=self.pred_color_map if self.instance_mode_pred else None,
            )
        else:
            self.pred_layer = None

        # Handle ground truth mask if available
        if self.true is not None:
            # Transpose ground truth data
            if self.instance_mode_true and self.true_instances is not None:
                true_data = self.transpose_data(self.true_instances, direction)
            else:
                true_data = self.transpose_data(self.true, direction)

            # Add the ground truth mask layer
            self.true_layer = self.add_mask_layer(
                data=true_data,
                name="Ground Truth",
                is_instance_mode=self.instance_mode_true
                and self.true_instances is not None,
                colors=self.true_colors,
                visible=true_visible,
                default_color="blue",
                color_map=self.true_color_map if self.instance_mode_true else None,
            )
        else:
            self.true_layer = None

        # Clear any temporary data
        self.clear_memory()

        end_time = time.time()
        print(f"Direction change completed in {end_time - start_time:.2f} seconds")

    def toggle_pred_instance_mode(self, state):
        """Toggle between binary and instance segmentation mode for prediction"""
        print(f"Toggling prediction instance mode to {bool(state)}")
        self.instance_mode_pred = bool(state)
        # Update the view to reflect the new mode
        self.change_direction(self.current_direction)
        # Force memory cleanup
        self.clear_memory()

    def toggle_true_instance_mode(self, state):
        """Toggle between binary and instance segmentation mode for ground truth"""
        print(f"Toggling ground truth instance mode to {bool(state)}")
        self.instance_mode_true = bool(state)
        # Update the view to reflect the new mode
        self.change_direction(self.current_direction)
        # Force memory cleanup
        self.clear_memory()

    def toggle_pred(self, state):
        """Toggle visibility of prediction mask"""
        if self.pred_layer:
            self.pred_layer.visible = bool(state)

    def toggle_true(self, state):
        """Toggle visibility of ground truth mask"""
        if self.true_layer:
            self.true_layer.visible = bool(state)


def view_volume_with_masks(volume=None, pred=None, true=None):
    """
    View a 3D volume with optional prediction and ground truth masks using napari

    Args:
        volume: 3D numpy array of the volume data, or None if not available
        pred: 3D numpy array of the prediction mask, or None if not available
        true: 3D numpy array of the ground truth mask, or None if not available
    """
    # Create a napari viewer
    viewer = napari.Viewer()

    # Add custom controls
    controls = ViewerControls(viewer, volume, pred, true)
    viewer.window.add_dock_widget(controls, name="Controls", area="right")

    # Start the napari event loop
    napari.run()


def create_empty_viewer():
    """
    Create an empty napari viewer with file loading controls
    """
    view_volume_with_masks()


def print_memory_usage(message="Current memory usage"):
    """Print current memory usage"""
    process = psutil.Process(os.getpid())
    mem = process.memory_info().rss / 1024 / 1024  # in MB
    print(f"{message}: {mem:.1f} MB")


def downsample_volume(volume, factor=2):
    """Downsample a 3D volume by the given factor to reduce memory usage

    Args:
        volume: 3D numpy array
        factor: Downsampling factor (default: 2)

    Returns:
        Downsampled volume
    """
    if factor <= 1:
        return volume

    # Downsample by taking every nth voxel
    return volume[::factor, ::factor, ::factor]


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    # Check if we should load default files or start with empty viewer
    load_default = os.environ.get("LOAD_DEFAULT", "true").lower() == "true"

    if not load_default:
        print("Starting with empty viewer - use GUI buttons to load files")
        create_empty_viewer()
        return

    # Print initial memory usage
    print_memory_usage("Initial memory usage")

    # Get downsampling factor from command line or use default
    downsample_factor = int(os.environ.get("DOWNSAMPLE", "1"))
    if downsample_factor > 1:
        print(f"Using downsampling factor: {downsample_factor}")

    # Get view mode from command line or use default (both, volume_only, pred_only, true_only)
    # view_mode = os.environ.get("VIEW_MODE", "both").lower()
    view_mode = "both"
    print(f"Using view mode: {view_mode}")

    # Load volume data
    volume = Volume(
        os.path.join(
            cfg.datasets_root,
            "main/mitoem/val/em/mito/em_1_0_0.zst",
        )
    )
    volume.load()
    volume.scale_volume_to((512, 512, 512))
    print_memory_usage("After loading volume")

    # Optionally downsample volume to reduce memory usage
    if downsample_factor > 1:
        volume.volume = downsample_volume(volume.volume, downsample_factor)
        print(f"Downsampled volume shape: {volume.volume.shape}")
        print_memory_usage("After downsampling volume")

    # Initialize masks to None
    pred_mask_volume = None
    true_mask_volume = None

    # Load prediction mask if needed
    if view_mode in ["both", "pred_only"]:
        try:
            pred_mask = Volume(
                os.path.join(
                    cfg.output_root,
                    "em_1_0_0_unet.zst",
                )
            )
            pred_mask.load()
            pred_mask.scale_volume_to((512, 512, 512))
            print_memory_usage("After loading prediction mask")

            # Optionally downsample prediction mask
            if downsample_factor > 1:
                pred_mask.volume = downsample_volume(
                    pred_mask.volume, downsample_factor
                )
                print(f"Downsampled prediction mask shape: {pred_mask.volume.shape}")
                print_memory_usage("After downsampling prediction mask")

            pred_mask_volume = pred_mask.volume
        except Exception as e:
            print(f"Error loading prediction mask: {e}")
            pred_mask_volume = None
    else:
        print("Skipping prediction mask loading based on view mode")

    # Load ground truth mask if needed
    if view_mode in ["both", "true_only"]:
        try:
            true_mask = Volume(
                os.path.join(
                    cfg.datasets_root,
                    "main/mitoem/val/seg/mito/em_0_0_0.zst",
                )
            )
            true_mask.load()
            true_mask.scale_volume_to((128, 512, 512))
            print_memory_usage("After loading ground truth mask")

            # Optionally downsample ground truth mask
            if downsample_factor > 1:
                true_mask.volume = downsample_volume(
                    true_mask.volume, downsample_factor
                )
                print(f"Downsampled ground truth mask shape: {true_mask.volume.shape}")
                print_memory_usage("After downsampling ground truth mask")

            true_mask_volume = true_mask.volume
        except Exception as e:
            print(f"Error loading ground truth mask: {e}")
            true_mask_volume = None
    else:
        print("Skipping ground truth mask loading based on view mode")

    # Force garbage collection before viewing
    gc.collect()
    print_memory_usage("After garbage collection")

    # View the volume with masks (either or both can be None)
    view_volume_with_masks(volume.volume, pred_mask_volume, true_mask_volume)


if __name__ == "__main__":
    main()
